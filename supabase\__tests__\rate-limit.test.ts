import { afterAll, beforeAll, expect, test } from "vitest";
import { createSetupHooks } from "./setup";
import { dbClient, serviceClient } from "./utils/client";
import { resetRateLimits } from "./utils/rate-limit";

createSetupHooks();

let originalMaxRequestsPerMinute: number;

beforeAll(async () => {
  const result = await dbClient.query(
    "SELECT max_requests_per_minute FROM app_core.config WHERE id = TRUE"
  );
  originalMaxRequestsPerMinute = result.rows[0]?.max_requests_per_minute ?? 0;

  await dbClient.query(
    "UPDATE app_core.config SET max_requests_per_minute = 5 WHERE id = TRUE"
  );
});

afterAll(async () => {
  await dbClient.query(
    "UPDATE app_core.config SET max_requests_per_minute = $1 WHERE id = TRUE",
    [originalMaxRequestsPerMinute]
  );
});

test("should enforce rate limiting after 5 requests within 1 minutes", async () => {
  await resetRateLimits();

  const requestsToMake = 6; // 5 allowed + 1 to trigger rate limit

  for (let i = 0; i < requestsToMake; i++) {
    const { error } = await serviceClient
      .schema("app_test")
      .rpc("test_request");

    if (i === requestsToMake) {
      expect(error?.code).toBe("429");
    }
  }
});
