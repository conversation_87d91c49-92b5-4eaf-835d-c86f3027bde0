import { test, expect, describe } from "vitest";
import { createSetupHooks } from "./setup";
import { mockCustomer, mockProvider } from "./mocks/user";
import { mockService } from "./mocks/service";
import { serviceClient } from "./utils/client";

createSetupHooks();

const customer = mockCustomer();
const provider = mockProvider();

const notApprovedService = mockService(provider, { approve: false });

test("should not be selectable by a customer if it is not approved", async () => {
  if (!customer.client) throw new Error("Customer client is undefined");
  if (!notApprovedService.providerServiceId)
    throw new Error("Service ID is undefined");

  const { data } = await customer.client
    .schema("app_provider")
    .from("service")
    .select("*")
    .eq("id", notApprovedService.providerServiceId)
    .single();

  expect(data).toBeNull();
});

const approvedPublishedService = mockService(provider, {
  service: { status: "published" },
  approve: true
});

test("should be selectable by a customer if it is approved and published", async () => {
  if (!provider.data) throw new Error("Provider data is undefined");
  if (!customer.client) throw new Error("Customer client is undefined");
  if (!approvedPublishedService.providerServiceId)
    throw new Error("Service ID is undefined");

  const { data } = await customer.client
    .schema("app_provider")
    .from("service")
    .select("*")
    .eq("id", approvedPublishedService.providerServiceId)
    .single();

  expect(data?.id).toBe(approvedPublishedService.providerServiceId);
});

const publishedNotApprovedService = mockService(provider, {
  service: { status: "published" },
  approve: false
});

test("should not be selectable by a customer if it is published but not approved", async () => {
  if (!provider.data) throw new Error("Provider data is undefined");
  if (!provider.client) throw new Error("Provider client is undefined");
  if (!customer.client) throw new Error("Customer client is undefined");
  if (!publishedNotApprovedService.providerServiceId)
    throw new Error("Service ID is undefined");

  const { data } = await customer.client
    .schema("app_provider")
    .from("service")
    .select("*")
    .eq("id", publishedNotApprovedService.providerServiceId)
    .single();

  expect(data).toBeNull();
});

const approvedDraftService = mockService(provider, {
  service: { status: "draft" },
  approve: true
});

test("should not be selectable by a customer if it is approved but in draft status", async () => {
  if (!provider.data) throw new Error("Provider data is undefined");
  if (!provider.client) throw new Error("Provider client is undefined");
  if (!customer.client) throw new Error("Customer client is undefined");
  if (!approvedDraftService.providerServiceId)
    throw new Error("Service ID is undefined");

  const serviceApprove = await serviceClient
    .schema("app_provider")
    .from("approved_service")
    .upsert({
      service_id: approvedDraftService.providerServiceId,
      user_id: provider.data?.id
    });

  expect(serviceApprove.error).toBeNull();

  const { data } = await customer.client
    .schema("app_provider")
    .from("service")
    .select("*")
    .eq("id", approvedDraftService.providerServiceId)
    .single();

  expect(data).toBeNull();
});

const serviceToModify = mockService(provider, {
  service: { status: "published" },
  approve: true
});

test("should become unselectable after modification and selectable again only after re-approval", async () => {
  if (!provider.data) throw new Error("Provider data is undefined");
  if (!provider.client) throw new Error("Provider client is undefined");
  if (!customer.client) throw new Error("Customer client is undefined");
  if (!serviceToModify.providerServiceId)
    throw new Error("Service ID is undefined");

  // try select with customer
  const { data: initialService, error: initialServiceError } =
    await customer.client
      .schema("app_provider")
      .from("service")
      .select("*")
      .eq("id", serviceToModify.providerServiceId)
      .single();

  expect(initialServiceError).toBeNull();
  expect(initialService).not.toBeNull();

  const serviceUpdate = await provider.client
    .schema("app_provider")
    .from("service")
    .update({ name: { en: "Modified Service" }, status: "published" })
    .eq("id", serviceToModify.providerServiceId)
    .select()
    .single();

  expect(serviceUpdate.data?.id).toBe(serviceToModify.providerServiceId);

  // try select with customer
  const { data: modifiedService, error: modifiedServiceError } =
    await customer.client
      .schema("app_provider")
      .from("service")
      .select("*")
      .eq("id", serviceToModify.providerServiceId)
      .single();

  expect(modifiedServiceError).not.toBeNull();
  expect(modifiedService).toBeNull();

  const serviceApprove = await serviceClient
    .schema("app_provider")
    .from("approved_service")
    .upsert({
      service_id: serviceToModify.providerServiceId,
      user_id: provider.data?.id
    });

  expect(serviceApprove.error).toBeNull();

  const { data: approvedService, error: approvedServiceError } =
    await customer.client
      .schema("app_provider")
      .from("service")
      .select("*")
      .eq("id", serviceToModify.providerServiceId)
      .single();

  expect(approvedServiceError).toBeNull();
  expect(approvedService).not.toBeNull();
});

const serviceWithoutNameOrService = mockService(provider, {
  service: { name: null, selected_service_id: null, status: "draft" },
  approve: false
});

test("should not be publishable without either a name or a selected service", async () => {
  if (!provider.client) throw new Error("Provider client is undefined");
  if (!serviceWithoutNameOrService.providerServiceId)
    throw new Error("Service ID is undefined");

  const publishUpdate = await provider.client
    .schema("app_provider")
    .from("service")
    .update({
      status: "published"
    })
    .eq("id", serviceWithoutNameOrService.providerServiceId);

  expect(publishUpdate.error).not.toBeNull();
});

const serviceWithoutPricing = mockService(provider, {
  service: { pricing_id: null, status: "draft" },
  approve: false
});

test("should not be publishable without a pricing plan", async () => {
  if (!provider.client) throw new Error("Provider client is undefined");
  if (!serviceWithoutPricing.providerServiceId)
    throw new Error("Service ID is undefined");

  const publishUpdate = await provider.client
    .schema("app_provider")
    .from("service")
    .update({
      status: "published"
    })
    .eq("id", serviceWithoutPricing.providerServiceId);

  expect(publishUpdate.error).not.toBeNull();
});

describe("Service Orderability: Approval and Publishing Status", () => {
  const publishedNotApproved = mockService(provider, {
    service: { status: "published" },
    approve: false
  });
  const approvedNotPublished = mockService(provider, {
    service: { status: "draft" },
    approve: true
  });

  test("should not be orderable if published but not approved", async () => {
    if (!customer.client) throw new Error("Customer client is undefined");
    if (!publishedNotApproved.providerServiceId)
      throw new Error("Service ID is undefined");

    const order = await customer.client
      .schema("app_provider")
      .rpc("submit_order", {
        p_service_id: publishedNotApproved.providerServiceId,
        p_unit_count: 1
      });

    expect(order.data?.id).toBeUndefined();
  });

  test("should not be orderable if approved but not published (in draft)", async () => {
    if (!customer.client) throw new Error("Customer client is undefined");
    if (!approvedNotPublished.providerServiceId)
      throw new Error("Service ID is undefined");

    const order = await customer.client
      .schema("app_provider")
      .rpc("submit_order", {
        p_service_id: approvedNotPublished.providerServiceId,
        p_unit_count: 1
      });

    expect(order.data?.id).toBeUndefined();
  });
});

describe("Service Modifier Orderability: Approval and Publishing Status", () => {
  const publishedNotApproved = mockService(provider, {
    serviceModifier: { status: "draft" },
    approve: false
  });
  const approvedNotPublished = mockService(provider, {
    serviceModifier: { status: "published" },
    approve: false
  });

  test("should not be orderable with a modifier that is published but not approved", async () => {
    if (!customer.client) throw new Error("Customer client is undefined");
    if (!publishedNotApproved.providerServiceId)
      throw new Error("Service ID is undefined");
    if (!publishedNotApproved.providerServiceModifierId)
      throw new Error("Service Modifier ID is undefined");

    const order = await customer.client
      .schema("app_provider")
      .rpc("submit_order", {
        p_service_id: publishedNotApproved.providerServiceId,
        p_unit_count: 1,
        p_service_modifier_ids: [publishedNotApproved.providerServiceModifierId]
      });

    expect(order.data?.id).toBeUndefined();
  });

  test("should not be orderable with a modifier that is approved but not published", async () => {
    if (!customer.client) throw new Error("Customer client is undefined");
    if (!approvedNotPublished.providerServiceId)
      throw new Error("Service ID is undefined");
    if (!approvedNotPublished.providerServiceModifierId)
      throw new Error("Service Modifier ID is undefined");

    const order = await customer.client
      .schema("app_provider")
      .rpc("submit_order", {
        p_service_id: approvedNotPublished.providerServiceId,
        p_unit_count: 1,
        p_service_modifier_ids: [approvedNotPublished.providerServiceModifierId]
      });

    expect(order.data?.id).toBeUndefined();
  });
});
