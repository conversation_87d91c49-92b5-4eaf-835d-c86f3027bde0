import { describe, test, expect, beforeEach, afterEach } from "vitest";
import { mockOrder, mockOrderEach } from "./mocks/order";
import { mockReview } from "./mocks/review";
import { mockService } from "./mocks/service";
import { serviceClient } from "./utils/client";
import { mockAdmin, mockCustomer, mockProvider } from "./mocks/user";
import { createSetupHooks } from "./setup";

createSetupHooks();

const customer = mockCustomer();
const provider = mockProvider();
const admin = mockAdmin();
const service = mockService(provider);
const order = mockOrderEach({ customer, provider, service });

describe("update provider performance when", () => {
  describe("refunds", () => {
    beforeEach(async () => {
      if (!provider.client) throw new Error("Provider client is undefined");
      if (!order.id) throw new Error("Order ID is undefined");

      // Provider accepts the order
      const acceptUpdate = await provider.client
        .schema("app_provider")
        .from("order")
        .update({ order_status: "accepted" })
        .eq("id", order.id);
      expect(acceptUpdate.error).toBeNull();

      // Provider completes the order
      const completeUpdate = await provider.client
        .schema("app_provider")
        .from("order")
        .update({ order_status: "completed" })
        .eq("id", order.id);
      expect(completeUpdate.error).toBeNull();
    });

    afterEach(async () => {
      if (!provider.data) throw new Error("Provider data is undefined");

      // Reset provider performance
      await serviceClient
        .schema("app_provider")
        .from("performance")
        .delete()
        .eq("user_id", provider.data.id);
    });

    test("refunds by provider increases refunds", async () => {
      if (!order.id) throw new Error("Order ID is undefined");
      if (!provider.data) throw new Error("Provider data is undefined");
      if (!provider.client) throw new Error("Provider client is undefined");

      // Get initial refunds count for the provider
      const initialPerformance = await serviceClient
        .schema("app_provider")
        .from("performance")
        .select("refunds")
        .eq("user_id", provider.data.id)
        .single();

      const initialRefunds = initialPerformance.data?.refunds || 0;

      // Provider refunds the order
      const refundUpdate = await provider.client
        .schema("app_provider")
        .from("order")
        .update({ order_status: "refunded" })
        .eq("id", order.id);

      expect(refundUpdate.error).toBeNull();

      // Get final refunds count for the provider
      const finalPerformance = await serviceClient
        .schema("app_provider")
        .from("performance")
        .select("refunds")
        .eq("user_id", provider.data.id)
        .single();

      const finalRefunds = finalPerformance.data?.refunds || 0;

      // Assert that refunds increased by 1
      expect(finalRefunds).toBe(initialRefunds + 1);
    });

    test("refunds by other roles increases refunds_by_intervention", async () => {
      if (!order.id) throw new Error("Order ID is undefined");
      if (!customer.client) throw new Error("User client is undefined");
      if (!provider.data) throw new Error("Provider data is undefined");
      if (!admin.client) throw new Error("Admin client is undefined");

      // User initiates a dispute
      const disputeUpdate = await customer.client
        .schema("app_provider")
        .from("order")
        .update({ order_status: "in_dispute" })
        .eq("id", order.id);
      expect(disputeUpdate.error).toBeNull();

      // Get initial refunds_by_intervention count for the provider
      const initialPerformance = await serviceClient
        .schema("app_provider")
        .from("performance")
        .select("refunds_by_intervention")
        .eq("user_id", provider.data.id)
        .single();

      const initialRefundsByIntervention =
        initialPerformance.data?.refunds_by_intervention || 0;

      // Admin refunds the disputed order
      const { error: adminRefundError } = await admin.client
        .schema("app_provider")
        .rpc("handle_disputed_order", {
          p_order_id: order.id,
          p_action: "refund"
        });

      expect(adminRefundError).toBeNull();

      // Get final refunds_by_intervention count for the provider
      const finalPerformance = await serviceClient
        .schema("app_provider")
        .from("performance")
        .select("refunds_by_intervention")
        .eq("user_id", provider.data.id)
        .single();

      const finalRefundsByIntervention =
        finalPerformance.data?.refunds_by_intervention || 0;

      // Assert that refunds_by_intervention increased by 1
      expect(finalRefundsByIntervention).toBe(initialRefundsByIntervention + 1);
    });
  });

  describe("an order is completed and archived", () => {
    const order = mockOrder({
      customer,
      provider,
      service,
      status: "completed"
    });

    test("completed_orders is incremented", async () => {
      if (!order.id) throw new Error("Order ID is undefined");
      if (!provider.data) throw new Error("Provider data is undefined");

      // Get initial completed_orders count for the provider
      const initialPerformance = await serviceClient
        .schema("app_provider")
        .from("performance")
        .select("completed_orders")
        .eq("user_id", provider.data.id)
        .single();

      const initialCompletedOrders =
        initialPerformance.data?.completed_orders || 0;

      // Delete the order to trigger archiving and performance update
      await serviceClient
        .schema("app_provider")
        .from("order")
        .delete()
        .eq("id", order.id);

      // Get final completed_orders count for the provider
      const finalPerformance = await serviceClient
        .schema("app_provider")
        .from("performance")
        .select("completed_orders")
        .eq("user_id", provider.data.id)
        .single();

      const finalCompletedOrders = finalPerformance.data?.completed_orders || 0;

      // Assert that completed_orders increased by 1
      expect(finalCompletedOrders).toBe(initialCompletedOrders + 1);
    });

    test("earned_soda is incremented", async () => {
      if (!order.id) throw new Error("Order ID is undefined");
      if (!provider.data) throw new Error("Provider data is undefined");

      // Get the soda_amount of the completed order
      const orderDetails = await serviceClient
        .schema("app_provider")
        .from("order")
        .select("soda_amount")
        .eq("id", order.id)
        .single();

      const orderSodaAmount = orderDetails.data?.soda_amount || 0;

      // Get initial earned_soda for the provider
      const initialPerformance = await serviceClient
        .schema("app_provider")
        .from("performance")
        .select("earned_soda")
        .eq("user_id", provider.data.id)
        .single();

      const initialEarnedSoda = initialPerformance.data?.earned_soda || 0;

      // Delete the order to trigger archiving and performance update
      await serviceClient
        .schema("app_provider")
        .from("order")
        .delete()
        .eq("id", order.id);

      // Get final earned_soda for the provider
      const finalPerformance = await serviceClient
        .schema("app_provider")
        .from("performance")
        .select("earned_soda")
        .eq("user_id", provider.data.id)
        .single();

      const finalEarnedSoda = finalPerformance.data?.earned_soda || 0;

      // Assert that earned_soda increased by the order's soda_amount
      expect(finalEarnedSoda).toBe(initialEarnedSoda + orderSodaAmount);
    });
  });

  describe("a review is approved", () => {
    const order = mockOrder({
      customer,
      provider,
      service,
      status: "completed"
    });
    mockReview({ order, customer, admin, approve: true });

    test("rating is updated", async () => {
      if (!provider.data) throw new Error("Provider data is undefined");

      // Get final rating for the provider
      const finalPerformance = await serviceClient
        .schema("app_provider")
        .from("performance")
        .select("rating")
        .eq("user_id", provider.data.id)
        .single();

      const finalRating = finalPerformance.data?.rating || 0;

      // Assert that rating is 5 (since one 5-star review was approved)
      expect(finalRating).toBe(5);
    });

    test("reviews is incremented", async () => {
      if (!provider.data) throw new Error("Provider data is undefined");

      // Get final reviews count for the provider
      const finalPerformance = await serviceClient
        .schema("app_provider")
        .from("performance")
        .select("reviews")
        .eq("user_id", provider.data.id)
        .single();

      const finalReviews = finalPerformance.data?.reviews || 0;

      // Assert that reviews increased by 1
      expect(finalReviews).toBe(1);
    });
  });
});
