import { test, expect, beforeAll } from "vitest";
import { createSetupHooks } from "./utils/createSetupHooks";
import { serviceClient } from "./utils/client";
import { mockCustomer } from "./mocks/user";

createSetupHooks();

const customer = mockCustomer();

beforeAll(async () => {
  if (!customer.data) throw new Error("Customer data is undefined");

  await serviceClient
    .schema("app_transaction")
    .from("config")
    .update({
      minimum_soda_withdrawal_amount: 100
    })
    .eq("id", true);

  await serviceClient.schema("app_transaction").from("wallet").insert({
    user_id: customer.data.id,
    soda_balance: 1000
  });
});

test("Allow withdrawal when amount meets minimum", async () => {
  if (!customer.client) throw new Error("Customer client is undefined");

  const inserting = await customer.client
    .schema("app_transaction")
    .from("withdrawal_request")
    .insert({
      soda_amount: 100,
      currency: "TRY"
    });

  expect(inserting.error).toBe(null);
});

test("Disallow withdrawal when amount is below minimum", async () => {
  if (!customer.client) throw new Error("Customer client is undefined");

  const inserting = await customer.client
    .schema("app_transaction")
    .from("withdrawal_request")
    .insert({
      soda_amount: 99,
      currency: "TRY"
    });

  expect(inserting.error).not.toBe(null);
});

test("Disallow withdrawal when wallet balance is insufficient", async () => {
  if (!customer.client) throw new Error("Customer client is undefined");

  const inserting = await customer.client
    .schema("app_transaction")
    .from("withdrawal_request")
    .insert({
      soda_amount: 1001, // More than the initial 1000 balance
      currency: "TRY"
    });

  expect(inserting.error).not.toBe(null);
});
