-- section SCHEMA
DROP SCHEMA IF EXISTS app_catalog CASCADE
;

CREATE SCHEMA IF NOT EXISTS app_catalog
;

GRANT USAGE ON SCHEMA app_catalog TO anon,
authenticated,
service_role
;

GRANT ALL ON ALL TABLES IN SCHEMA app_catalog TO anon,
authenticated,
service_role
;

GRANT ALL ON ALL ROUTINES IN SCHEMA app_catalog TO anon,
authenticated,
service_role
;

GRANT ALL ON ALL SEQUENCES IN SCHEMA app_catalog TO anon,
authenticated,
service_role
;

ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA app_catalog
GRANT ALL ON TABLES TO anon,
authenticated,
service_role
;

ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA app_catalog
GRANT ALL ON ROUTINES TO anon,
authenticated,
service_role
;

ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA app_catalog
GRANT ALL ON SEQUENCES TO anon,
authenticated,
service_role
;

-- !section
-- section ENUMS
-- anchor FIELD_TYPE
CREATE TYPE app_catalog.FIELD_TYPE AS ENUM(
  'text',
  'boolean',
  'select',
  'multiselect'
)
;

-- !section
--------------------------------------------------------------------------------
-- section TABLES
--------------------------------------------------------------------------------
-- anchor category
CREATE TABLE app_catalog.category (
  id UUID PRIMARY KEY DEFAULT GEN_RANDOM_UUID(),
  parent_category_id UUID REFERENCES app_catalog.category (id) ON DELETE SET NULL,
  NAME JSONB NOT NULL,
  description JSONB,
  icon TEXT,
  cover TEXT,
  slug TEXT UNIQUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE (NAME)
)
;

-- anchor activity
CREATE TABLE app_catalog.activity (
  id UUID PRIMARY KEY DEFAULT GEN_RANDOM_UUID(),
  category_id UUID REFERENCES app_catalog.category (id) ON DELETE SET NULL,
  NAME JSONB NOT NULL,
  description JSONB,
  color JSONB CHECK (
    color IS NULL
    OR app_core.is_valid_color_jsonb (color)
  ),
  slug TEXT UNIQUE,
  service_count INTEGER NOT NULL DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE (category_id, NAME)
)
;

-- anchor tag
CREATE TABLE app_catalog.tag (
  id UUID PRIMARY KEY DEFAULT GEN_RANDOM_UUID(),
  NAME JSONB NOT NULL,
  description JSONB,
  slug TEXT UNIQUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
)
;

-- anchor activity_tag
CREATE TABLE app_catalog.activity_tag (
  activity_id UUID REFERENCES app_catalog.activity (id) ON DELETE SET NULL,
  tag_id UUID REFERENCES app_catalog.tag (id) ON DELETE SET NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  PRIMARY KEY (tag_id, activity_id)
)
;

-- anchor field
CREATE TABLE app_catalog.field (
  id UUID PRIMARY KEY DEFAULT GEN_RANDOM_UUID(),
  NAME JSONB NOT NULL,
  description JSONB,
  TYPE app_catalog.FIELD_TYPE NOT NULL DEFAULT 'text',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
)
;

-- anchor category_field
CREATE TABLE app_catalog.category_field (
  field_id UUID REFERENCES app_catalog.field (id) ON DELETE SET NULL,
  category_id UUID REFERENCES app_catalog.category (id) ON DELETE SET NULL,
  PRIMARY KEY (field_id, category_id)
)
;

-- anchor activity_field
CREATE TABLE app_catalog.activity_field (
  field_id UUID REFERENCES app_catalog.field (id) ON DELETE SET NULL,
  activity_id UUID REFERENCES app_catalog.activity (id) ON DELETE SET NULL,
  PRIMARY KEY (field_id, activity_id)
)
;

-- anchor field_option
CREATE TABLE app_catalog.field_option (
  id UUID PRIMARY KEY DEFAULT GEN_RANDOM_UUID(),
  field_id UUID REFERENCES app_catalog.field (id) ON DELETE CASCADE,
  NAME JSONB NOT NULL,
  description JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
)
;

-- anchor category_field_option
CREATE TABLE app_catalog.category_field_option (
  field_option_id UUID REFERENCES app_catalog.field_option (id) ON DELETE SET NULL,
  category_id UUID REFERENCES app_catalog.category (id) ON DELETE CASCADE,
  PRIMARY KEY (field_option_id, category_id)
)
;

-- anchor activity_field_option
CREATE TABLE app_catalog.activity_field_option (
  field_option_id UUID REFERENCES app_catalog.field_option (id) ON DELETE CASCADE,
  activity_id UUID REFERENCES app_catalog.activity (id) ON DELETE CASCADE,
  PRIMARY KEY (field_option_id, activity_id)
)
;

-- anchor pricing
CREATE TABLE app_catalog.pricing (
  id UUID PRIMARY KEY DEFAULT GEN_RANDOM_UUID(),
  NAME JSONB NOT NULL,
  description JSONB,
  icon TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
)
;

-- anchor activity_pricing
CREATE TABLE app_catalog.activity_pricing (
  activity_id UUID REFERENCES app_catalog.activity (id) ON DELETE CASCADE,
  pricing_id UUID REFERENCES app_catalog.pricing (id) ON DELETE CASCADE,
  PRIMARY KEY (activity_id, pricing_id)
)
;

-- anchor category_pricing
CREATE TABLE app_catalog.category_pricing (
  category_id UUID REFERENCES app_catalog.category (id) ON DELETE CASCADE,
  pricing_id UUID REFERENCES app_catalog.pricing (id) ON DELETE CASCADE,
  PRIMARY KEY (category_id, pricing_id)
)
;

-- anchor service
CREATE TABLE app_catalog.service (
  id UUID PRIMARY KEY DEFAULT GEN_RANDOM_UUID(),
  NAME JSONB NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
)
;

-- anchor activity_service
CREATE TABLE app_catalog.activity_service (
  activity_id UUID REFERENCES app_catalog.activity (id) ON DELETE CASCADE,
  service_id UUID REFERENCES app_catalog.service (id) ON DELETE CASCADE,
  PRIMARY KEY (activity_id, service_id)
)
;

-- anchor category_service
CREATE TABLE app_catalog.category_service (
  category_id UUID REFERENCES app_catalog.category (id) ON DELETE CASCADE,
  service_id UUID REFERENCES app_catalog.service (id) ON DELETE CASCADE,
  PRIMARY KEY (category_id, service_id)
)
;

-- !section
-- section TRIGGERS
-- anchor category
CREATE TRIGGER category_validate_locale_columns BEFORE INSERT
OR
UPDATE ON app_catalog.category FOR EACH ROW
EXECUTE FUNCTION app_core.validate_locale_columns ('name', 'description')
;

CREATE TRIGGER category_check_circular_dependency BEFORE INSERT
OR
UPDATE ON app_catalog.category FOR EACH ROW
EXECUTE FUNCTION app_core.check_circular_dependency (
  'app_catalog',
  'category',
  'id',
  'parent_category_id',
  'Circular dependency detected in category hierarchy'
)
;

-- anchor activity
CREATE TRIGGER activity_validate_locale_columns BEFORE INSERT
OR
UPDATE ON app_catalog.activity FOR EACH ROW
EXECUTE FUNCTION app_core.validate_locale_columns ('name', 'description')
;

-- anchor tag
CREATE TRIGGER tag_validate_locale_columns BEFORE INSERT
OR
UPDATE ON app_catalog.tag FOR EACH ROW
EXECUTE FUNCTION app_core.validate_locale_columns ('name', 'description')
;

-- anchor field
CREATE TRIGGER field_validate_locale_columns BEFORE INSERT
OR
UPDATE ON app_catalog.field FOR EACH ROW
EXECUTE FUNCTION app_core.validate_locale_columns ('name', 'description')
;

-- anchor field_option
CREATE TRIGGER field_option_validate_locale_columns BEFORE INSERT
OR
UPDATE ON app_catalog.field_option FOR EACH ROW
EXECUTE FUNCTION app_core.validate_locale_columns ('name', 'description')
;

-- anchor pricing
CREATE TRIGGER pricing_validate_locale_columns BEFORE INSERT
OR
UPDATE ON app_catalog.pricing FOR EACH ROW
EXECUTE FUNCTION app_core.validate_locale_columns ('name', 'description')
;

-- anchor service
CREATE TRIGGER service_validate_locale_columns BEFORE INSERT
OR
UPDATE ON app_catalog.service FOR EACH ROW
EXECUTE FUNCTION app_core.validate_locale_columns ('name')
;

-- !section
-- section RLS POLICIES
-- anchor category
ALTER TABLE app_catalog.category ENABLE ROW LEVEL SECURITY
;

CREATE POLICY category_select_all ON app_catalog.category FOR
SELECT
  USING (TRUE)
;

CREATE POLICY category_insert_admin ON app_catalog.category FOR INSERT
WITH
  CHECK (
    app_access.has_capability ('catalog.category.all.insert')
  )
;

CREATE POLICY category_update_admin ON app_catalog.category
FOR UPDATE
  USING (
    app_access.has_capability ('catalog.category.all.update')
  )
;

CREATE POLICY category_delete_admin ON app_catalog.category FOR DELETE USING (
  app_access.has_capability ('catalog.category.all.delete')
)
;

-- anchor activity
ALTER TABLE app_catalog.activity ENABLE ROW LEVEL SECURITY
;

CREATE POLICY activity_select_all ON app_catalog.activity FOR
SELECT
  USING (TRUE)
;

CREATE POLICY activity_insert_admin ON app_catalog.activity FOR INSERT
WITH
  CHECK (
    app_access.has_capability ('catalog.activity.all.insert')
  )
;

CREATE POLICY activity_update_admin ON app_catalog.activity
FOR UPDATE
  USING (
    app_access.has_capability ('catalog.activity.all.update')
  )
;

CREATE POLICY activity_delete_admin ON app_catalog.activity FOR DELETE USING (
  app_access.has_capability ('catalog.activity.all.delete')
)
;

-- anchor tag
ALTER TABLE app_catalog.tag ENABLE ROW LEVEL SECURITY
;

CREATE POLICY tag_select_all ON app_catalog.tag FOR
SELECT
  USING (TRUE)
;

CREATE POLICY tag_insert_admin ON app_catalog.tag FOR INSERT
WITH
  CHECK (
    app_access.has_capability ('catalog.tag.all.insert')
  )
;

CREATE POLICY tag_update_admin ON app_catalog.tag
FOR UPDATE
  USING (
    app_access.has_capability ('catalog.tag.all.update')
  )
;

CREATE POLICY tag_delete_admin ON app_catalog.tag FOR DELETE USING (
  app_access.has_capability ('catalog.tag.all.delete')
)
;

-- anchor activity_tag
ALTER TABLE app_catalog.activity_tag ENABLE ROW LEVEL SECURITY
;

CREATE POLICY activity_tag_select_all ON app_catalog.activity_tag FOR
SELECT
  USING (TRUE)
;

CREATE POLICY activity_tag_insert_admin ON app_catalog.activity_tag FOR INSERT
WITH
  CHECK (
    app_access.has_capability (
      'catalog.activity_tag.all.insert'
    )
  )
;

CREATE POLICY activity_tag_delete_admin ON app_catalog.activity_tag FOR DELETE USING (
  app_access.has_capability (
    'catalog.activity_tag.all.delete'
  )
)
;

-- anchor field
ALTER TABLE app_catalog.field ENABLE ROW LEVEL SECURITY
;

CREATE POLICY field_select_all ON app_catalog.field FOR
SELECT
  USING (TRUE)
;

CREATE POLICY field_insert_admin ON app_catalog.field FOR INSERT
WITH
  CHECK (
    app_access.has_capability ('catalog.field.all.insert')
  )
;

CREATE POLICY field_update_admin ON app_catalog.field
FOR UPDATE
  USING (
    app_access.has_capability ('catalog.field.all.update')
  )
;

CREATE POLICY field_delete_admin ON app_catalog.field FOR DELETE USING (
  app_access.has_capability ('catalog.field.all.delete')
)
;

-- anchor category_field
ALTER TABLE app_catalog.category_field ENABLE ROW LEVEL SECURITY
;

CREATE POLICY category_field_select_all ON app_catalog.category_field FOR
SELECT
  USING (TRUE)
;

CREATE POLICY category_field_insert_admin ON app_catalog.category_field FOR INSERT
WITH
  CHECK (
    app_access.has_capability (
      'catalog.category_field.all.insert'
    )
  )
;

CREATE POLICY category_field_delete_admin ON app_catalog.category_field FOR DELETE USING (
  app_access.has_capability (
    'catalog.category_field.all.delete'
  )
)
;

-- anchor activity_field
ALTER TABLE app_catalog.activity_field ENABLE ROW LEVEL SECURITY
;

CREATE POLICY activity_field_select_all ON app_catalog.activity_field FOR
SELECT
  USING (TRUE)
;

CREATE POLICY activity_field_insert_admin ON app_catalog.activity_field FOR INSERT
WITH
  CHECK (
    app_access.has_capability (
      'catalog.activity_field.all.insert'
    )
  )
;

CREATE POLICY activity_field_delete_admin ON app_catalog.activity_field FOR DELETE USING (
  app_access.has_capability (
    'catalog.activity_field.all.delete'
  )
)
;

-- anchor field_option
ALTER TABLE app_catalog.field_option ENABLE ROW LEVEL SECURITY
;

CREATE POLICY field_option_select_all ON app_catalog.field_option FOR
SELECT
  USING (TRUE)
;

CREATE POLICY field_option_insert_admin ON app_catalog.field_option FOR INSERT
WITH
  CHECK (
    app_access.has_capability (
      'catalog.field_option.all.insert'
    )
  )
;

CREATE POLICY field_option_update_admin ON app_catalog.field_option
FOR UPDATE
  USING (
    app_access.has_capability (
      'catalog.field_option.all.update'
    )
  )
;

CREATE POLICY field_option_delete_admin ON app_catalog.field_option FOR DELETE USING (
  app_access.has_capability (
    'catalog.field_option.all.delete'
  )
)
;

-- anchor category_field_option
ALTER TABLE app_catalog.category_field_option ENABLE ROW LEVEL SECURITY
;

CREATE POLICY category_field_option_select_all ON app_catalog.category_field_option FOR
SELECT
  USING (TRUE)
;

CREATE POLICY category_field_option_insert_admin ON app_catalog.category_field_option FOR INSERT
WITH
  CHECK (
    app_access.has_capability (
      'catalog.category_field_option.all.insert'
    )
  )
;

CREATE POLICY category_field_option_delete_admin ON app_catalog.category_field_option FOR DELETE USING (
  app_access.has_capability (
    'catalog.category_field_option.all.delete'
  )
)
;

-- anchor activity_field_option
ALTER TABLE app_catalog.activity_field_option ENABLE ROW LEVEL SECURITY
;

CREATE POLICY activity_field_option_select_all ON app_catalog.activity_field_option FOR
SELECT
  USING (TRUE)
;

CREATE POLICY activity_field_option_insert_admin ON app_catalog.activity_field_option FOR INSERT
WITH
  CHECK (
    app_access.has_capability (
      'catalog.activity_field_option.all.insert'
    )
  )
;

CREATE POLICY activity_field_option_delete_admin ON app_catalog.activity_field_option FOR DELETE USING (
  app_access.has_capability (
    'catalog.activity_field_option.all.delete'
  )
)
;

-- anchor pricing
ALTER TABLE app_catalog.pricing ENABLE ROW LEVEL SECURITY
;

CREATE POLICY pricing_select_all ON app_catalog.pricing FOR
SELECT
  USING (TRUE)
;

CREATE POLICY pricing_insert_admin ON app_catalog.pricing FOR INSERT
WITH
  CHECK (
    app_access.has_capability ('catalog.pricing.all.insert')
  )
;

CREATE POLICY pricing_update_admin ON app_catalog.pricing
FOR UPDATE
  USING (
    app_access.has_capability ('catalog.pricing.all.update')
  )
;

CREATE POLICY pricing_delete_admin ON app_catalog.pricing FOR DELETE USING (
  app_access.has_capability ('catalog.pricing.all.delete')
)
;

-- anchor activity_pricing
ALTER TABLE app_catalog.activity_pricing ENABLE ROW LEVEL SECURITY
;

CREATE POLICY activity_pricing_select_all ON app_catalog.activity_pricing FOR
SELECT
  USING (TRUE)
;

CREATE POLICY activity_pricing_insert_admin ON app_catalog.activity_pricing FOR INSERT
WITH
  CHECK (
    app_access.has_capability (
      'catalog.activity_pricing.all.insert'
    )
  )
;

CREATE POLICY activity_pricing_delete_admin ON app_catalog.activity_pricing FOR DELETE USING (
  app_access.has_capability (
    'catalog.activity_pricing.all.delete'
  )
)
;

-- anchor category_pricing
ALTER TABLE app_catalog.category_pricing ENABLE ROW LEVEL SECURITY
;

CREATE POLICY category_pricing_select_all ON app_catalog.category_pricing FOR
SELECT
  USING (TRUE)
;

CREATE POLICY category_pricing_insert_admin ON app_catalog.category_pricing FOR INSERT
WITH
  CHECK (
    app_access.has_capability (
      'catalog.category_pricing.all.insert'
    )
  )
;

CREATE POLICY category_pricing_delete_admin ON app_catalog.category_pricing FOR DELETE USING (
  app_access.has_capability (
    'catalog.category_pricing.all.delete'
  )
)
;

-- anchor service
ALTER TABLE app_catalog.service ENABLE ROW LEVEL SECURITY
;

CREATE POLICY service_select_all ON app_catalog.service FOR
SELECT
  USING (TRUE)
;

CREATE POLICY service_insert_admin ON app_catalog.service FOR INSERT
WITH
  CHECK (
    app_access.has_capability ('catalog.service.all.insert')
  )
;

CREATE POLICY service_update_admin ON app_catalog.service
FOR UPDATE
  USING (
    app_access.has_capability ('catalog.service.all.update')
  )
;

CREATE POLICY service_delete_admin ON app_catalog.service FOR DELETE USING (
  app_access.has_capability ('catalog.service.all.delete')
)
;

-- anchor activity_service
ALTER TABLE app_catalog.activity_service ENABLE ROW LEVEL SECURITY
;

CREATE POLICY activity_service_select_all ON app_catalog.activity_service FOR
SELECT
  USING (TRUE)
;

CREATE POLICY activity_service_insert_admin ON app_catalog.activity_service FOR INSERT
WITH
  CHECK (
    app_access.has_capability (
      'catalog.activity_service.all.insert'
    )
  )
;

CREATE POLICY activity_service_delete_admin ON app_catalog.activity_service FOR DELETE USING (
  app_access.has_capability (
    'catalog.activity_service.all.delete'
  )
)
;

-- anchor category_service
ALTER TABLE app_catalog.category_service ENABLE ROW LEVEL SECURITY
;

CREATE POLICY category_service_select_all ON app_catalog.category_service FOR
SELECT
  USING (TRUE)
;

CREATE POLICY category_service_insert_admin ON app_catalog.category_service FOR INSERT
WITH
  CHECK (
    app_access.has_capability (
      'catalog.category_service.all.insert'
    )
  )
;

CREATE POLICY category_service_delete_admin ON app_catalog.category_service FOR DELETE USING (
  app_access.has_capability (
    'catalog.category_service.all.delete'
  )
)
;

-- !section
-- section CAPABILITIES
-- anchor admin
SELECT
  app_access.define_role_capability (
    'admin',
    ARRAY[
      'catalog.category.all.insert',
      'catalog.category.all.update',
      'catalog.category.all.delete',
      'catalog.activity.all.insert',
      'catalog.activity.all.update',
      'catalog.activity.all.delete',
      'catalog.tag.all.insert',
      'catalog.tag.all.update',
      'catalog.tag.all.delete',
      'catalog.activity_tag.all.insert',
      'catalog.activity_tag.all.delete',
      'catalog.field.all.insert',
      'catalog.field.all.update',
      'catalog.field.all.delete',
      'catalog.category_field.all.insert',
      'catalog.category_field.all.delete',
      'catalog.activity_field.all.insert',
      'catalog.activity_field.all.delete',
      'catalog.field_option.all.insert',
      'catalog.field_option.all.update',
      'catalog.field_option.all.delete',
      'catalog.category_field_option.all.insert',
      'catalog.category_field_option.all.delete',
      'catalog.activity_field_option.all.insert',
      'catalog.activity_field_option.all.delete',
      'catalog.pricing.all.insert',
      'catalog.pricing.all.update',
      'catalog.pricing.all.delete',
      'catalog.activity_pricing.all.insert',
      'catalog.activity_pricing.all.delete',
      'catalog.category_pricing.all.insert',
      'catalog.category_pricing.all.delete',
      'catalog.service.all.insert',
      'catalog.service.all.update',
      'catalog.service.all.delete',
      'catalog.activity_service.all.insert',
      'catalog.activity_service.all.delete',
      'catalog.category_service.all.insert',
      'catalog.category_service.all.delete'
    ]
  )
;

-- !section