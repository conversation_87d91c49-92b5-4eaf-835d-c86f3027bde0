import { describe, expect, test } from "vitest";
import { Database } from "shared/lib/supabase/database";
import { mockCustomer, mockProvider } from "./mocks/user";
import { mockService } from "./mocks/service";
import { createSetupHooks } from "./utils/createSetupHooks";
import { serviceClient } from "./utils/client";
import { mockOrder } from "./mocks/order";

createSetupHooks();

const senderProhibitedTransitions: { from: string; to: string[] }[] = [
  {
    from: "pending",
    to: ["accepted", "rejected", "completed", "in_dispute", "refunded"]
  },
  {
    from: "accepted",
    to: ["pending", "rejected", "completed", "cancelled", "refunded"]
  },
  {
    from: "rejected",
    to: [
      "pending",
      "accepted",
      "completed",
      "cancelled",
      "in_dispute",
      "refunded"
    ]
  },
  {
    from: "completed",
    to: ["pending", "accepted", "rejected", "cancelled", "refunded"]
  },
  {
    from: "cancelled",
    to: [
      "pending",
      "accepted",
      "rejected",
      "completed",
      "in_dispute",
      "refunded"
    ]
  },
  {
    from: "in_dispute",
    to: [
      "pending",
      "accepted",
      "rejected",
      "completed",
      "cancelled",
      "refunded"
    ]
  },
  {
    from: "refunded",
    to: [
      "pending",
      "accepted",
      "rejected",
      "completed",
      "cancelled",
      "in_dispute"
    ]
  }
];

const receiverProhibitedTransitions: { from: string; to: string[] }[] = [
  {
    from: "pending",
    to: ["completed", "cancelled", "in_dispute", "refunded"]
  },
  {
    from: "accepted",
    to: ["pending", "rejected", "cancelled", "in_dispute", "refunded"]
  },
  {
    from: "rejected",
    to: [
      "pending",
      "accepted",
      "completed",
      "cancelled",
      "in_dispute",
      "refunded"
    ]
  },
  {
    from: "completed",
    to: ["pending", "accepted", "rejected", "cancelled", "in_dispute"]
  },
  {
    from: "cancelled",
    to: [
      "pending",
      "accepted",
      "rejected",
      "completed",
      "in_dispute",
      "refunded"
    ]
  },
  {
    from: "in_dispute",
    to: ["pending", "accepted", "rejected", "completed", "cancelled"]
  },
  {
    from: "refunded",
    to: [
      "pending",
      "accepted",
      "rejected",
      "completed",
      "cancelled",
      "in_dispute"
    ]
  }
];

type OrderStatus = Database["app_provider"]["Enums"]["order_status"];

const customer = mockCustomer(100000000);
const provider = mockProvider();
const service = mockService(provider);

describe("as sender", () => {
  senderProhibitedTransitions.forEach(({ from, to }) => {
    to.forEach((targetStatus) => {
      describe(`${from} ->| ${targetStatus}`, () => {
        const order = mockOrder({
          status: from as OrderStatus,
          customer,
          provider,
          service
        });

        test("try to change order status", async () => {
          if (!customer.client) throw new Error("Customer client is undefined");
          if (!order.id) throw new Error("Order ID is undefined");

          await customer.client
            .schema("app_provider")
            .from("order")
            .update({ order_status: targetStatus as OrderStatus })
            .eq("id", order.id);

          const checkOrderStatus = await serviceClient
            .schema("app_provider")
            .from("order")
            .select("order_status")
            .eq("id", order.id)
            .single();

          expect(checkOrderStatus.data?.order_status).not.toBe(targetStatus);
        });
      });
    });
  });
});

describe("as receiver", () => {
  receiverProhibitedTransitions.forEach(({ from, to }) => {
    to.forEach((targetStatus) => {
      describe(`${from} ->| ${targetStatus}`, () => {
        const order = mockOrder({
          status: from as OrderStatus,
          customer,
          provider,
          service
        });

        test("try to change order status", async () => {
          if (!provider.client) throw new Error("Provider client is undefined");
          if (!order.id) throw new Error("Order ID is undefined");

          await provider.client
            .schema("app_provider")
            .from("order")
            .update({ order_status: targetStatus as OrderStatus })
            .eq("id", order.id);

          const checkOrderStatus = await serviceClient
            .schema("app_provider")
            .from("order")
            .select("order_status")
            .eq("id", order.id)
            .single();

          expect(checkOrderStatus.data?.order_status).not.toBe(targetStatus);
        });
      });
    });
  });
});
