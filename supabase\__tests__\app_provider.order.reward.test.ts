import { expect, test } from "vitest";
import { mockService } from "./mocks/service";
import { mockCustomer, mockProvider } from "./mocks/user";
import { serviceClient } from "./utils/client";
import { mockOrder } from "./mocks/order";
import { createSetupHooks } from "./setup";

createSetupHooks();

const customer = mockCustomer();
const provider = mockProvider();
const service = mockService(provider);
const mockCompletedOrder = mockOrder({
  status: "completed",
  service,
  customer,
  provider
});

test("customer and provider are rewarded with caps on completed order archival", async () => {
  if (!customer.data) throw new Error("Customer data is undefined");
  if (!provider.data) throw new Error("Provider data is undefined");
  if (!mockCompletedOrder.id) throw new Error("Order ID is undefined");

  // Get initial cap balances
  const { data: initialCustomerWallet } = await serviceClient
    .schema("app_transaction")
    .from("wallet")
    .select("cap_balance")
    .eq("user_id", customer.data.id)
    .single();

  const { data: initialProviderWallet } = await serviceClient
    .schema("app_transaction")
    .from("wallet")
    .select("cap_balance")
    .eq("user_id", provider.data.id)
    .single();

  const initialCustomerCaps = initialCustomerWallet?.cap_balance ?? 0;
  const initialProviderCaps = initialProviderWallet?.cap_balance ?? 0;

  // Get reward amounts from config
  const { data: rewardConfig } = await serviceClient
    .schema("app_provider")
    .from("config")
    .select(
      "cap_reward_to_customer_for_completed_order, cap_reward_to_provider_for_completed_order"
    )
    .single();

  const customerReward =
    rewardConfig?.cap_reward_to_customer_for_completed_order ?? 0;
  const providerReward =
    rewardConfig?.cap_reward_to_provider_for_completed_order ?? 0;

  // Delete the order to trigger archival
  const { error: archiveCompletedError } = await serviceClient
    .schema("app_provider")
    .from("order")
    .delete()
    .eq("id", mockCompletedOrder.id);

  expect(archiveCompletedError).toBeNull();

  // Get final cap balances
  const { data: finalCustomerWallet } = await serviceClient
    .schema("app_transaction")
    .from("wallet")
    .select("cap_balance")
    .eq("user_id", customer.data.id)
    .single();

  const { data: finalProviderWallet } = await serviceClient
    .schema("app_transaction")
    .from("wallet")
    .select("cap_balance")
    .eq("user_id", provider.data.id)
    .single();

  // Assert new balances
  expect(finalCustomerWallet?.cap_balance).toBe(
    initialCustomerCaps + customerReward
  );
  expect(finalProviderWallet?.cap_balance).toBe(
    initialProviderCaps + providerReward
  );
});
