import { expect, test } from "vitest";
import { createSetupHooks } from "./setup";
import { mockAdmin, mockCustomer } from "./mocks/user";

createSetupHooks();

const customer = mockCustomer();
const admin = mockAdmin();

test("Admin can assign roles", async () => {
  if (!admin.client) throw new Error("Admin client is undefined");
  if (!customer.data) throw new Error("Customer data is undefined");

  const user = customer.data;

  const assignRole = await admin.client
    .schema("app_access")
    .rpc("assign_role_to_user", {
      v_user_id: user.id,
      v_role_name: "provider"
    });

  expect(assignRole.data).toBe(true);

  const providerRoleIdSelect = await admin.client
    .schema("app_access")
    .from("role")
    .select("id")
    .eq("name", "provider")
    .single();

  expect(providerRoleIdSelect.error).toBeNull();

  const userRoleSelect = await admin.client
    .schema("app_access")
    .from("user_role")
    .select("*")
    .eq("user_id", user.id)
    .eq("role_id", Number(providerRoleIdSelect.data?.id))
    .single();

  expect(userRoleSelect.data?.user_id).toBe(user.id);
});

test("Normal user cannot assign roles", async () => {
  if (!customer.client) throw new Error("Customer client is undefined");
  if (!customer.data) throw new Error("Customer data is undefined");

  const user = customer.data;

  const assignRole = await customer.client
    .schema("app_access")
    .rpc("assign_role_to_user", {
      v_user_id: user.id,
      v_role_name: "provider"
    });

  expect(assignRole.error).not.toBeNull();
});
