import { test, expect, afterAll } from "vitest";
import { createSetupHooks } from "./utils/createSetupHooks";
import { readFileSync } from "fs";
import { fileTypeFromBuffer } from "file-type";
import { mockCustomer } from "./mocks/user";

createSetupHooks();

const customer = mockCustomer();

const pngPath = "supabase/__tests__/objects/test-upsert.png";
const webpPath = "supabase/__tests__/objects/test-upsert.webp";

afterAll(async () => {
  if (!customer.data) throw new Error("Customer data is undefined");
  if (!customer.client) throw new Error("Customer data is undefined");

  await customer.client.storage.from("avatar").remove([customer.data.id]);
});

test("Object ids should be same after upsert.", async () => {
  if (!customer.data) throw new Error("Customer data is undefined");
  if (!customer.client) throw new Error("Customer data is undefined");

  const png = readFileSync(pngPath);
  const pngType = await fileTypeFromBuffer(png);
  const webp = readFileSync(webpPath);
  const webpType = await fileTypeFromBuffer(webp);

  const pngInsertion = await customer.client.storage
    .from("avatar")
    .upload(customer.data.id, png, {
      upsert: true,
      metadata: {
        description: "png"
      },
      contentType: pngType?.mime
    });

  expect(pngInsertion.error).toBeNull();

  const pngInfo = await customer.client.storage
    .from("avatar")
    .info(customer.data.id);

  expect(pngInfo.data?.metadata?.description).toBe("png");

  const webpInsertion = await customer.client.storage
    .from("avatar")
    .upload(customer.data.id, webp, {
      upsert: true,
      metadata: {
        description: "webp"
      },
      contentType: webpType?.mime
    });

  expect(webpInsertion.error).toBeNull();

  const webpInfo = await customer.client.storage
    .from("avatar")
    .info(customer.data.id);

  expect(webpInfo.data?.metadata?.description).toBe("webp");

  expect(pngInfo.data?.id).toBe(webpInfo.data?.id);
});
