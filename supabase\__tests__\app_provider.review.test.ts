import { test, expect, describe } from "vitest";
import { serviceClient } from "./utils/client";
import { mockAdmin, mockCustomer, mockProvider } from "./mocks/user";
import { mockService } from "./mocks/service";
import { mockOrder } from "./mocks/order";
import { mockReview } from "./mocks/review";
import { createSetupHooks } from "./setup";

createSetupHooks();

const admin = mockAdmin();

describe("completing an order", () => {
  const customer = mockCustomer();
  const provider = mockProvider();
  const service = mockService(provider);
  const order = mockOrder({
    status: "completed",
    customer,
    provider,
    service
  });

  test("creates review pass", async () => {
    if (!customer.data) throw new Error("Customer data is undefined");
    if (!provider.data) throw new Error("Provider data is undefined");
    if (!service.selectedActivityId)
      throw new Error("Selected Activity ID is undefined");
    if (!order.id) throw new Error("Order ID is undefined");

    const { data: reviewPass, error: reviewPassError } = await serviceClient
      .schema("app_provider")
      .from("review_pass")
      .select("*")
      .eq("user_id", customer.data.id)
      .eq("order_id", order.id)
      .single();

    expect(reviewPassError).toBeNull();
    expect(reviewPass).toBeDefined();
    expect(reviewPass?.user_id).toBe(customer.data.id);
    expect(reviewPass?.provider_id).toBe(provider.data.id);
    expect(reviewPass?.activity_id).toBe(service.selectedActivityId);
    expect(reviewPass?.order_id).toBe(order.id);
  });
});

describe("submitting a review", () => {
  const customer = mockCustomer();
  const provider = mockProvider();
  const service = mockService(provider);
  const order = mockOrder({
    status: "completed",
    customer,
    provider,
    service
  });

  const review = mockReview({ order, customer, admin, approve: false });

  test("rewards caps to customer", async () => {
    if (!customer.data) throw new Error("Customer data is undefined");
    if (!customer.client) throw new Error("Customer client is undefined");

    // Get cap reward amount from config
    const { data: config, error: configError } = await serviceClient
      .schema("app_provider")
      .from("config")
      .select("*")
      .single();

    expect(configError).toBeNull();
    expect(config).toBeDefined();

    const capReward = config?.cap_reward_to_customer_for_review_submission || 0;

    // Get final cap balance
    const { data: finalWallet, error: finalWalletError } = await serviceClient
      .schema("app_transaction")
      .from("wallet")
      .select("cap_balance")
      .eq("user_id", customer.data.id)
      .single();

    expect(finalWalletError).toBeNull();
    expect(finalWallet).toBeDefined();

    const finalCapBalance = finalWallet?.cap_balance || 0;

    expect(finalCapBalance - capReward).toBe(0);
  });

  test("prevents updating review ", async () => {
    if (!customer.client) throw new Error("Customer client is undefined");
    if (!review.id) throw new Error("Review ID is undefined");

    const { data: initialReview } = await serviceClient
      .schema("app_provider")
      .from("review")
      .select("comment")
      .eq("id", review.id)
      .single();

    const originalComment = initialReview!.comment;

    await customer.client
      .schema("app_provider")
      .from("review")
      .update({ comment: "Updated comment" })
      .eq("id", review.id);

    // No error expected as we are just verifying with a select statement
    // Verify the comment has not been updated

    const { data: updatedReview, error: fetchError } = await serviceClient
      .schema("app_provider")
      .from("review")
      .select("comment")
      .eq("id", review.id)
      .single();

    expect(fetchError).toBeNull();
    expect(updatedReview?.comment).toBe(originalComment);
  });

  test("prevents selecting a review pending approval", async () => {
    if (!provider.client) throw new Error("Provider client is undefined");
    if (!review.id) throw new Error("Review ID is undefined");

    const { data: unapprovedReview, error: unapprovedReviewError } =
      await provider.client
        .schema("app_provider")
        .from("review")
        .select("*")
        .eq("id", review.id)
        .single();

    expect(unapprovedReviewError).not.toBeNull();
    expect(unapprovedReview).toBeNull();
  });
});

describe("approving a review", () => {
  const customer = mockCustomer();
  const provider = mockProvider();
  const service = mockService(provider);
  const order = mockOrder({
    status: "completed",
    customer,
    provider,
    service
  });

  const review = mockReview({ order, customer, admin, approve: true });

  test("rewards caps to provider", async () => {
    if (!customer.data) throw new Error("Customer data is undefined");
    if (!customer.client) throw new Error("Customer client is undefined");
    if (!provider.data) throw new Error("Provider data is undefined");
    if (!admin.client) throw new Error("Admin client is undefined");

    // Get cap reward amount from config
    const { data: config, error: configError } = await serviceClient
      .schema("app_provider")
      .from("config")
      .select("*")
      .single();

    expect(configError).toBeNull();
    expect(config).toBeDefined();

    const capReward = config?.cap_reward_to_provider_for_review_approval || 0;

    // Get final cap balance for the provider
    const { data: finalWallet, error: finalWalletError } = await serviceClient
      .schema("app_transaction")
      .from("wallet")
      .select("cap_balance")
      .eq("user_id", provider.data.id)
      .single();

    expect(finalWalletError).toBeNull();
    expect(finalWallet).toBeDefined();

    const finalCapBalance = finalWallet?.cap_balance || 0;

    // Assert that provider's cap balance increased by the reward amount
    expect(finalCapBalance - capReward).toBe(0);
  });

  test("allows it to be selected", async () => {
    if (!review.id) return;
    if (!admin.client) throw new Error("Admin client is undefined");
    if (!customer.data) throw new Error("Customer data is undefined");
    if (!provider.client) throw new Error("Provider client is undefined");

    const { data: approvedReview, error: approvedReviewError } =
      await provider.client
        .schema("app_provider")
        .from("review")
        .select("*")
        .eq("id", review.id)
        .single();

    expect(approvedReviewError).toBeNull();
    expect(approvedReview).toBeDefined();
    expect(approvedReview?.id).toBe(review.id);
  });
});
