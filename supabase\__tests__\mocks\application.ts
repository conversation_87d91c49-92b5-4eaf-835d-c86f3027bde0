import { afterAll, beforeAll, expect } from "vitest";
import { MockUser } from "./user";
import { mockService } from "./service";
import { serviceClient } from "../utils/client";

type MockApplication = {
  id?: string;
};

export function mockApplication(customer: MockUser) {
  const application: MockApplication = {};

  beforeAll(async () => {
    if (!customer.client) throw new Error("Customer client is undefined");
    if (!customer.data) throw new Error("Customer data is undefined");

    const submitApplication = await customer.client
      .schema("app_provider")
      .from("application")
      .insert({
        user_id: customer.data.id,
        application_status: "draft"
      });

    expect(submitApplication.error).toBeNull();

    const viewApplication = await customer.client
      .schema("app_provider")
      .from("application")
      .select("*")
      .eq("user_id", customer.data.id)
      .single();

    expect(viewApplication.data?.user_id).toBe(customer.data.id);

    application.id = viewApplication.data?.user_id;
  });

  afterAll(async () => {
    if (!customer.data) throw new Error("Customer data is undefined");

    // clean application
    await serviceClient
      .schema("app_provider")
      .from("application")
      .delete()
      .eq("user_id", customer.data?.id);
  });

  return application;
}

export function mockFilledOutApplication(customer: MockUser) {
  const application = mockApplication(customer);
  const service = mockService(customer);

  return { application, service };
}
