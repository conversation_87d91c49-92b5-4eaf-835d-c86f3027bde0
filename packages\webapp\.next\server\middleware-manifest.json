{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_42788bc2._.js", "server/edge/chunks/41428_@formatjs_intl-localematcher_lib_d637b060._.js", "server/edge/chunks/f7359_@supabase_auth-js_dist_module_d23006fd._.js", "server/edge/chunks/node_modules__pnpm_ecf55bff._.js", "server/edge/chunks/[root-of-the-server]__569f5a63._.js", "server/edge/chunks/packages_webapp_edge-wrapper_2e2e15f3.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico|ingest|.*\\..*).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|ingest|.*\\..*).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "BwpAgcBgXqjbQWOpyrp+dSFW0SqENk1GPyl8AcBB52I=", "__NEXT_PREVIEW_MODE_ID": "d10a738656a39b057810b9a8829461f4", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "71d1610095daf7fd5a81fbad613d21a8c83f7dc0fe5c3877194878dc1f47dd03", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "98910f5bbde54aa9a063e9ef760239c18c0e18ef0c9a0e3beb462d870ca31e46"}}}, "sortedMiddleware": ["/"], "functions": {}}