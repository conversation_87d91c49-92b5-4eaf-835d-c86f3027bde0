import { describe, test, expect } from "vitest";
import { mockCatalogActivity } from "./mocks/app_catalog.activity";
import { mockService } from "./mocks/service";
import { mockAdmin, mockProvider } from "./mocks/user";

describe("Activity Service Count", () => {
  const admin = mockAdmin();
  const provider = mockProvider();
  const activity = mockCatalogActivity({ admin });

  mockService(provider, {
    activityId: activity.id,
    approve: true
  });

  mockService(provider, {
    activityId: activity.id,
    approve: true
  });

  test("should increment on service approval", async () => {
    if (!activity.id) throw new Error("Activity ID is undefined");
    if (!admin.client) throw new Error("Admin client is undefined");

    const checkActivity = await admin.client
      .schema("app_catalog")
      .from("activity")
      .select("*")
      .eq("id", activity.id)
      .single();

    expect(checkActivity.data?.service_count).toBe(2);
  });
});
